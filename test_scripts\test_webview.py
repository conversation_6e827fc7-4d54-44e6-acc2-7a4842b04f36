#!/usr/bin/env python3
"""
测试WebView桌面应用

简化版本，用于测试pywebview是否正常工作
"""

import webview
import threading
import time
import uvicorn
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.backend.main import app

def start_server():
    """启动FastAPI服务器"""
    uvicorn.run(
        app,
        host="127.0.0.1",
        port=8001,  # 使用不同端口避免冲突
        log_level="error"
    )

def main():
    """主函数"""
    print("🚀 启动测试桌面应用")
    
    # 在后台线程启动服务器
    server_thread = threading.Thread(target=start_server, daemon=True)
    server_thread.start()
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(3)
    
    # 创建WebView窗口
    print("🖥️  创建桌面窗口")
    try:
        webview.create_window(
            'YouTube数据抓取应用 - 测试版',
            'http://127.0.0.1:8001',
            width=1200,
            height=800,
            resizable=True
        )
        
        print("✅ 启动WebView")
        webview.start(debug=True)  # 启用调试模式
        
    except Exception as e:
        print(f"❌ 创建窗口失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
