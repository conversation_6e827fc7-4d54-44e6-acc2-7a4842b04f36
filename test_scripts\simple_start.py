#!/usr/bin/env python3
"""
简单启动器 - 用于测试
"""

import sys
import webbrowser
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    print("🚀 启动YouTube数据抓取应用")
    
    # 直接打开浏览器到现有的服务器
    url = "http://127.0.0.1:8000"
    print(f"🌐 打开浏览器: {url}")
    
    try:
        webbrowser.open(url)
        print("✅ 浏览器已打开")
        print("💡 如果页面无法访问，请先运行: python main_web.py --web-only")
    except Exception as e:
        print(f"❌ 打开浏览器失败: {e}")

if __name__ == "__main__":
    main()
