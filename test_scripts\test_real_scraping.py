#!/usr/bin/env python3
"""
真实数据抓取测试脚本

测试真实的YouTube数据抓取功能，确保集成了比特浏览器
"""

import requests
import json
import time
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

BASE_URL = "http://127.0.0.1:8000"

def create_test_profile():
    """创建测试Profile"""
    print("🧪 创建测试Profile...")
    
    test_profile = {
        "name": "真实测试Profile",
        "browser_type": "bitbrowser",
        "browser_window_id": "test-window-real",  # 请替换为真实的窗口ID
        "youtube_account": "<EMAIL>",    # 请替换为真实的YouTube账号
        "is_active": True,
        "earliest_date": "2024-01-01",
        "notes": "这是一个真实数据抓取测试Profile"
    }
    
    response = requests.post(f"{BASE_URL}/api/profiles", json=test_profile)
    print(f"   状态码: {response.status_code}")
    
    if response.status_code == 200:
        created_profile = response.json()
        print(f"   创建成功，ID: {created_profile['id']}")
        return created_profile['id']
    else:
        print(f"   创建失败: {response.text}")
        return None

def test_real_scraping(profile_id):
    """测试真实数据抓取"""
    if not profile_id:
        print("❌ 没有可用的Profile，跳过测试")
        return
    
    print("🚀 开始真实数据抓取测试...")
    print("⚠️  注意：这将启动比特浏览器并进行真实的YouTube数据抓取")
    print("⚠️  请确保：")
    print("   1. 比特浏览器已安装并运行")
    print("   2. 窗口ID正确")
    print("   3. YouTube账号已登录")
    
    # 启动任务
    print("\n1. 启动真实抓取任务...")
    task_data = {
        "profile_ids": [profile_id],
        "task_type": "youtube_scraping"
    }
    
    response = requests.post(f"{BASE_URL}/api/tasks/start", json=task_data)
    print(f"   状态码: {response.status_code}")
    
    if response.status_code != 200:
        print(f"   任务启动失败: {response.text}")
        return
    
    task = response.json()
    task_id = task['task_id']
    print(f"   任务启动成功，ID: {task_id}")
    
    # 监控任务状态
    print("\n2. 监控任务状态...")
    print("   这可能需要几分钟时间，请耐心等待...")
    
    max_wait_time = 300  # 最多等待5分钟
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        try:
            response = requests.get(f"{BASE_URL}/api/tasks/{task_id}")
            if response.status_code == 200:
                task_status = response.json()
                status = task_status['status']
                progress = task_status['progress']
                message = task_status.get('message', '')
                
                print(f"   状态: {status}, 进度: {progress}%, 消息: {message}")
                
                if status in ['completed', 'failed', 'stopped']:
                    print(f"\n✅ 任务结束，最终状态: {status}")
                    
                    # 显示结果
                    if status == 'completed' and 'results' in task_status:
                        print("\n📊 抓取结果:")
                        results = task_status['results']
                        for profile_id, result in results.items():
                            print(f"   Profile: {result['profile_name']}")
                            print(f"   状态: {result['status']}")
                            print(f"   消息: {result['message']}")
                            
                            if result['status'] == 'success' and result['data']:
                                data = result['data']
                                print(f"   抓取视频数: {data.get('videos_scraped', 0)}")
                                print(f"   总观看量: {data.get('total_views', 0)}")
                                print(f"   总点赞数: {data.get('total_likes', 0)}")
                                print(f"   总评论数: {data.get('total_comments', 0)}")
                                
                                # 显示前3个视频的详细信息
                                videos = data.get('videos', [])
                                if videos:
                                    print(f"\n   前3个视频详情:")
                                    for i, video in enumerate(videos[:3]):
                                        print(f"     {i+1}. {video.get('title', '未知标题')}")
                                        print(f"        观看量: {video.get('views', 0)}")
                                        print(f"        点赞数: {video.get('likes', 0)}")
                                        print(f"        评论数: {video.get('comments', 0)}")
                                        print(f"        点赞率: {video.get('like_ratio', 0):.2%}")
                    
                    return status == 'completed'
                    
            else:
                print(f"   获取任务状态失败: {response.status_code}")
                
        except Exception as e:
            print(f"   监控过程中出错: {e}")
        
        time.sleep(5)  # 每5秒检查一次
    
    print("⏰ 任务超时，可能仍在后台运行")
    return False

def main():
    """主函数"""
    print("🎬 YouTube真实数据抓取测试")
    print("=" * 60)
    
    try:
        # 测试服务器连接
        response = requests.get(BASE_URL, timeout=5)
        if response.status_code != 200:
            print("❌ 服务器连接失败")
            return
        print("✅ 服务器连接正常")
        
        # 创建测试Profile
        profile_id = create_test_profile()
        
        if profile_id:
            # 询问用户是否继续
            print("\n" + "="*60)
            print("⚠️  重要提醒：")
            print("1. 请确保比特浏览器已安装并运行")
            print("2. 请将上面创建的Profile中的窗口ID替换为真实的比特浏览器窗口ID")
            print("3. 请确保对应的YouTube账号已登录")
            print("4. 这将进行真实的数据抓取，不是模拟数据")
            print("="*60)
            
            user_input = input("\n是否继续进行真实数据抓取测试？(y/N): ")
            if user_input.lower() in ['y', 'yes']:
                # 执行真实抓取测试
                success = test_real_scraping(profile_id)
                if success:
                    print("\n🎉 真实数据抓取测试成功！")
                else:
                    print("\n❌ 真实数据抓取测试失败")
            else:
                print("👋 用户取消测试")
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保应用正在运行")
        print("   启动命令: python main_web.py --web-only")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
