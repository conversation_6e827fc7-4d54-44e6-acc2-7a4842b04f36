#!/usr/bin/env python3
"""
API测试脚本

测试Profile管理和任务执行API是否正常工作
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:8000"

def test_profiles_api():
    """测试Profile管理API"""
    print("🧪 测试Profile管理API")
    
    # 1. 获取Profile列表
    print("1. 获取Profile列表...")
    response = requests.get(f"{BASE_URL}/api/profiles")
    print(f"   状态码: {response.status_code}")
    profiles = response.json()
    print(f"   Profile数量: {len(profiles)}")
    
    # 2. 创建测试Profile
    print("2. 创建测试Profile...")
    test_profile = {
        "name": "测试Profile",
        "browser_type": "bitbrowser",
        "browser_window_id": "test-window-123",
        "youtube_account": "<EMAIL>",
        "is_active": True,
        "earliest_date": "2024-01-01",
        "notes": "这是一个测试Profile"
    }
    
    response = requests.post(f"{BASE_URL}/api/profiles", json=test_profile)
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        created_profile = response.json()
        print(f"   创建成功，ID: {created_profile['id']}")
        return created_profile['id']
    else:
        print(f"   创建失败: {response.text}")
        return None

def test_tasks_api(profile_id):
    """测试任务执行API"""
    if not profile_id:
        print("❌ 没有可用的Profile，跳过任务测试")
        return
    
    print("🧪 测试任务执行API")
    
    # 1. 启动任务
    print("1. 启动任务...")
    task_data = {
        "profile_ids": [profile_id],
        "task_type": "youtube_scraping"
    }
    
    response = requests.post(f"{BASE_URL}/api/tasks/start", json=task_data)
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        task = response.json()
        task_id = task['task_id']
        print(f"   任务启动成功，ID: {task_id}")
        
        # 2. 监控任务状态
        print("2. 监控任务状态...")
        for i in range(10):  # 最多等待10次
            time.sleep(1)
            response = requests.get(f"{BASE_URL}/api/tasks/{task_id}")
            if response.status_code == 200:
                task_status = response.json()
                status = task_status['status']
                progress = task_status['progress']
                print(f"   第{i+1}次检查 - 状态: {status}, 进度: {progress}%")
                
                if status in ['completed', 'failed', 'stopped']:
                    print(f"   任务结束，最终状态: {status}")
                    break
            else:
                print(f"   获取任务状态失败: {response.status_code}")
                break
    else:
        print(f"   任务启动失败: {response.text}")

def test_websocket():
    """测试WebSocket连接"""
    print("🧪 测试WebSocket连接")
    try:
        import websocket
        
        def on_message(ws, message):
            print(f"   收到消息: {message}")
        
        def on_error(ws, error):
            print(f"   WebSocket错误: {error}")
        
        def on_close(ws, close_status_code, close_msg):
            print("   WebSocket连接已关闭")
        
        def on_open(ws):
            print("   WebSocket连接已建立")
            # 发送心跳
            ws.send(json.dumps({"type": "ping", "timestamp": int(time.time())}))
        
        ws = websocket.WebSocketApp(f"ws://127.0.0.1:8000/ws",
                                  on_open=on_open,
                                  on_message=on_message,
                                  on_error=on_error,
                                  on_close=on_close)
        
        # 运行3秒后关闭
        import threading
        def close_after_delay():
            time.sleep(3)
            ws.close()
        
        threading.Thread(target=close_after_delay, daemon=True).start()
        ws.run_forever()
        
    except ImportError:
        print("   需要安装websocket-client: pip install websocket-client")
    except Exception as e:
        print(f"   WebSocket测试失败: {e}")

def main():
    """主函数"""
    print("🚀 开始API测试")
    print("=" * 50)
    
    try:
        # 测试服务器连接
        response = requests.get(BASE_URL, timeout=5)
        if response.status_code != 200:
            print("❌ 服务器连接失败")
            return
        print("✅ 服务器连接正常")
        
        # 测试Profile API
        profile_id = test_profiles_api()
        
        # 测试任务API
        test_tasks_api(profile_id)
        
        # 测试WebSocket（可选）
        # test_websocket()
        
        print("\n✅ API测试完成")
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保应用正在运行")
        print("   启动命令: python main_web.py --web-only")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
